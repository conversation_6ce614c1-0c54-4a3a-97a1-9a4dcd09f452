<?php

namespace App\Http\Controllers\Api\Main;

use App\Http\Controllers\Controller;
use App\Models\Api\Main\Day;
use App\Models\Api\Main\Lessen;
use App\Models\Api\Main\Notification;
use App\Models\Api\Main\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request as HttpRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RequestsController extends Controller
{
    /**
     * Display a listing of requests for admins
     */
    public function index(HttpRequest $request): JsonResponse
    {
        $requests = Request::with([
            'teacher',
            'section',
            'group',
            'module',
            'classRome',
            'reviewedBy'
        ])
        ->when($request->has('status'), function ($query) use ($request) {
            $query->where('status', $request->status);
        })
        ->orderBy('created_at', 'desc')
        ->paginate(10);

        return response()->json($requests);
    }

    /**
     * Store a newly created request
     */
    public function store(HttpRequest $request): JsonResponse
    {
        $validated = $request->validate([
            'teacher_id' => 'required|exists:teachers,id',
            'section_id' => 'nullable|exists:sections,id',
            'group_id' => 'nullable|exists:groups,id',
            'module_id' => 'required|exists:modules,id',
            'class_rome_id' => 'required|exists:class_romes,id',
            'day' => 'required|in:mon,tue,wed,thu,fri,sat,sun',
            'start_time' => 'required|date_format:H:i:s',
            'end_time' => 'required|date_format:H:i:s|after:start_time',
            'type' => 'required|in:td,tp,course',
            'message' => 'nullable|string|max:1000',
        ]);

        $teacherRequest = Request::create($validated);

        return response()->json([
            'message' => 'Request submitted successfully',
            'request' => $teacherRequest->load([
                'teacher',
                'section',
                'group',
                'module',
                'classRome'
            ])
        ], 201);
    }

    /**
     * Display the specified request
     */
    public function show(Request $request): JsonResponse
    {
        return response()->json([
            'request' => $request->load([
                'teacher',
                'section',
                'group',
                'module',
                'classRome',
                'reviewedBy'
            ])
        ]);
    }

    /**
     * Update request status (approve/reject)
     */
    public function update(HttpRequest $httpRequest, Request $request): JsonResponse
    {
        $validated = $httpRequest->validate([
            'status' => 'required|in:approved,rejected',
            'admin_response' => 'nullable|string|max:1000',
        ]);

        $user = Auth::user();
        $admin = $user->key->keyable;

        DB::beginTransaction();

        try {
            // Update request status
            $request->update([
                'status' => $validated['status'],
                'admin_response' => $validated['admin_response'] ?? null,
                'reviewed_by' => $admin->id,
                'reviewed_at' => now(),
            ]);

            $lessonId = null;

            // If approved, create the lesson
            if ($validated['status'] === 'approved') {
                \Log::info('Creating lesson from approved request', ['request_id' => $request->id]);
                $lessonId = $this->createLessonFromRequest($request);
                \Log::info('Lesson created successfully', ['lesson_id' => $lessonId, 'request_id' => $request->id]);
            }

            // Create notification for teacher
            $this->createNotificationForTeacher($request, $validated['status'], $validated['admin_response'] ?? null, $lessonId);

            DB::commit();

            \Log::info('Request approval completed', [
                'request_id' => $request->id,
                'status' => $validated['status'],
                'lesson_created' => $validated['status'] === 'approved',
                'lesson_id' => $lessonId
            ]);

            return response()->json([
                'message' => 'Request updated successfully',
                'request' => $request->load([
                    'teacher',
                    'section',
                    'group',
                    'module',
                    'classRome',
                    'reviewedBy'
                ]),
                'lesson_created' => $validated['status'] === 'approved',
                'lesson_id' => $lessonId
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'message' => 'Failed to process request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified request
     */
    public function destroy(Request $request): JsonResponse
    {
        $request->delete();

        return response()->json([
            'message' => 'Request deleted successfully'
        ]);
    }

    /**
     * Get pending requests count for notifications
     */
    public function pendingCount(): JsonResponse
    {
        $count = Request::pending()->count();

        return response()->json([
            'count' => $count
        ]);
    }

    /**
     * Get teacher's own requests
     */
    public function teacherRequests(HttpRequest $request): JsonResponse
    {
        $user = Auth::user();
        $teacher = $user->key->keyable;

        $requests = Request::where('teacher_id', $teacher->id)
            ->with([
                'section',
                'group',
                'module',
                'classRome',
                'reviewedBy'
            ])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($requests);
    }

    /**
     * Create lesson from approved request
     */
    private function createLessonFromRequest(Request $request): int
    {
        // Find or create the day for the section/group
        $timeTable = null;

        if ($request->section_id) {
            $timeTable = $request->section->timeTable;
        } elseif ($request->group_id) {
            $timeTable = $request->group->timeTable;
        }

        if (!$timeTable) {
            throw new \Exception('No timetable found for this section/group');
        }

        // Find or create the day
        $dayName = $request->day;
        $day = Day::where('time_table_id', $timeTable->id)
            ->where('name', $dayName)
            ->first();

        if (!$day) {
            $day = Day::create([
                'time_table_id' => $timeTable->id,
                'name' => $dayName,
            ]);
        }

        // Create the lesson
        $lesson = Lessen::create([
            'day_id' => $day->id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'type' => $request->type,
            'module_id' => $request->module_id,
            'teacher_id' => $request->teacher_id,
            'class_rome_id' => $request->class_rome_id,
        ]);

        return $lesson->id;
    }

    /**
     * Create notification for teacher
     */
    private function createNotificationForTeacher(Request $request, string $status, ?string $adminResponse, ?int $lessonId): void
    {
        $teacher = $request->teacher;
        $teacherUser = $teacher->key->user;



        if ($status === 'approved') {
            Notification::createRequestApproved(
                $teacherUser->id,
                $request->id,
                $lessonId
            );
        } else {
            Notification::createRequestRejected(
                $teacherUser->id,
                $request->id,
                $adminResponse
            );
        }
    }
}
