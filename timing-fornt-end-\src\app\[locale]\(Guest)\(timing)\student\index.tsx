import TimingTableGroup from "./groupTimng";
import { getGroups } from "@/lib/server/actions/group/groupActions";
import PrintButton from "./PrintButton";

export default async function Index() {
    const data = await getGroups();
    console.log('Student Timetable Data:', JSON.stringify(data, null, 2));

    if (!data.timeTableGroup) {
        console.error('Missing group timetable data:', data);
        return <div>No timetable data available</div>;
    }

    if (!data.groupInfo) {
        console.error('Missing group info:', data);
        return <div>No group information available</div>;
    }

    return (
        <>
            <div id="printable-timetable">
                <TimingTableGroup data={data.timeTableGroup} group={data.groupInfo.number} />
            </div>
            <div className="flex justify-center mt-6">
                <PrintButton />
            </div>
        </>
    )
}

