"use client";

import { TimeTableEntity } from "@/lib/server/types/sectionTiming/studentGroupTiming";
import { DashContent, DashContenTitle, DashContentTable, TableTd, TableTdMain, TableThead, TableTr } from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { useState, useEffect } from "react";

const STANDARD_SLOTS = [
    { start: "08:00", end: "09:30" },
    { start: "09:30", end: "11:00" },
    { start: "11:00", end: "12:30" },
    { start: "12:30", end: "14:00" },
    { start: "14:00", end: "15:30" },
    { start: "15:30", end: "17:00" },
];

const DAY_LABELS = {
    sat: "Saturday",
    sun: "Sunday",
    mon: "Monday",
    tue: "Tuesday",
    wed: "Wednesday",
    thu: "Thursday",
};

const DAYS = ["sat", "sun", "mon", "tue", "wed", "thu"];

export default function TimingTableGroup({ data, group }: { data: TimeTableEntity, group: number }) {
    const [timingDate] = useState<TimeTableEntity>(data);
    
    useEffect(() => {
        console.log('Group Timing Data:', data);
        console.log('Group Timing Days:', data?.days);
    }, [data]);

    // Map day names to their data for easy lookup
    const dayMap = {};
    if (timingDate?.days) {
        timingDate.days.forEach(day => {
            dayMap[day.name.toLowerCase().slice(0,3)] = day;
        });
    }

    if (!timingDate?.days) {
        return (
            <DashContent>
                <DashContenTitle>
                    Timing group {group}
                </DashContenTitle>
                <div className="flex flex-col gap-4">
                    <div>No timetable data available</div>
                </div>
            </DashContent>
        );
    }

    return (
        <DashContent>
            <DashContenTitle>
                My Class Schedule - Group {group}
            </DashContenTitle>
            <div className="flex flex-col gap-4">
                <DashContentTable>
                    <TableThead list={["Day", ...STANDARD_SLOTS.map(slot => `${slot.start}–${slot.end}`)]} />
                    <tbody>
                        {DAYS.map(dayKey => {
                            const day = dayMap[dayKey];
                            return (
                                <TableTr key={dayKey}>
                                    <TableTdMain value={DAY_LABELS[dayKey]} />
                                    {STANDARD_SLOTS.map(slot => {
                                        let session = null;
                                        if (day) {
                                            session = day.lessens.find(
                                                l => l.start_time.slice(0,5) === slot.start && l.end_time.slice(0,5) === slot.end
                                            );
                                        }
                                        return (
                                            <TableTd key={slot.start}>
                                                {session ? (
                                                    <div className="flex flex-col gap-0.5 p-1">
                                                        <span className="font-semibold text-xs">
                                                            {session.module.name}
                                                            <span className="ml-1 px-1 py-0.5 rounded bg-blue-100 text-blue-700 text-[10px] font-bold uppercase align-middle">{session.type}</span>
                                                        </span>
                                                        <span className="text-[11px] text-gray-500">
                                                            {session.teacher.name} {session.teacher.last} &bull; class: {session.class_rome.number}
                                                        </span>
                                                    </div>
                                                ) : (
                                                    <div>—</div>
                                                )}
                                            </TableTd>
                                        );
                                    })}
                                </TableTr>
                            );
                        })}
                    </tbody>
                </DashContentTable>
            </div>
        </DashContent>
    )
}
