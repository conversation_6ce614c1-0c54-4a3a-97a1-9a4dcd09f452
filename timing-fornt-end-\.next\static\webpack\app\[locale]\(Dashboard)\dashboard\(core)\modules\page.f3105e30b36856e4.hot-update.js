"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/modules/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx":
/*!**************************************************************************************!*\
  !*** ./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx ***!
  \**************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateModuleDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_ui_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ui/components/global/Buttons/Button */ \"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx\");\n/* harmony import */ var _lib_ui_components_global_Dialog_Dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ui/components/global/Dialog/Dialog */ \"(app-pages-browser)/./src/lib/ui/components/global/Dialog/Dialog.tsx\");\n/* harmony import */ var _lib_ui_forms_module_CreateModuleForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/ui/forms/module/CreateModuleForm */ \"(app-pages-browser)/./src/lib/ui/forms/module/CreateModuleForm.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CreateModuleDialog() {\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSuccess = ()=>{\n        // Close dialog immediately and refresh\n        setOpen(false);\n        // Use window.location.reload for a hard refresh to ensure cache is cleared\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                mode: \"filled\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\CreateModuleDialog.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 41\n                }, void 0),\n                onClick: ()=>setOpen(true),\n                children: \"Create Module\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\CreateModuleDialog.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_global_Dialog_Dialog__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: open,\n                onClose: ()=>setOpen(false),\n                title: \"Create Module\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_forms_module_CreateModuleForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    onSuccess: handleSuccess\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\CreateModuleDialog.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\CreateModuleDialog.tsx\",\n                lineNumber: 25,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreateModuleDialog, \"ytWOlNORoNjKCJyRctHL6U+Vztg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CreateModuleDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateModuleDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e79a8f3258f4\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcTWVtb2lyZVxcY29kZSBsYXN0IHZlcnNpb24gVjNcXEZpIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlNzlhOGYzMjU4ZjRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});