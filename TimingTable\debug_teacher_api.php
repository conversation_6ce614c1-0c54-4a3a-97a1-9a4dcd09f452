<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Api\Main\Lessen;
use App\Models\Api\Users\Teacher;
use Illuminate\Support\Facades\Auth;

echo "=== TEACHER API DEBUG ===\n\n";

// Get Teacher Lamia
$teacher = Teacher::where('name', 'Lamia')->first();
if (!$teacher) {
    echo "❌ Teacher Lamia not found\n";
    exit;
}

echo "👩‍🏫 Teacher: {$teacher->name} {$teacher->last} (ID: {$teacher->id})\n\n";

// Authenticate as teacher
Auth::login($teacher->key->user);
echo "✅ Authenticated as teacher\n\n";

// Test the teacher method directly
$controller = new \App\Http\Controllers\Api\Main\GroupsController();
$response = $controller->teacher();
$data = $response->getData(true);

echo "📡 Teacher API Response:\n";
echo "Status: " . $response->getStatusCode() . "\n\n";

if (isset($data['lessons'])) {
    foreach ($data['lessons'] as $dayKey => $dayData) {
        if ($dayData && isset($dayData['lessens'])) {
            echo "📅 Day Key: '{$dayKey}' (Original: '{$dayData['name']}')\n";
            echo "   Lessons: " . count($dayData['lessens']) . "\n";
            
            foreach ($dayData['lessens'] as $lesson) {
                echo "   - ID: {$lesson['id']}, Time: {$lesson['start_time']}-{$lesson['end_time']}, Type: {$lesson['type']}\n";
            }
            echo "\n";
        }
    }
} else {
    echo "❌ No lessons data found\n";
}

// Also check the raw database query
echo "=== RAW DATABASE QUERY ===\n";
$lessons = Lessen::where('teacher_id', $teacher->id)
    ->with(['day', 'classRome', 'module', 'teacher'])
    ->orderBy('start_time')
    ->get();

echo "Total lessons in database: " . $lessons->count() . "\n\n";

foreach ($lessons as $lesson) {
    echo "Lesson ID: {$lesson->id}\n";
    echo "  Day: {$lesson->day->name} (ID: {$lesson->day->id})\n";
    echo "  Time: {$lesson->start_time}-{$lesson->end_time}\n";
    echo "  Type: {$lesson->type}\n";
    echo "  Module: {$lesson->module->name}\n";
    echo "  Classroom: {$lesson->classRome->number}\n\n";
}

echo "=== DEBUG COMPLETE ===\n"; 