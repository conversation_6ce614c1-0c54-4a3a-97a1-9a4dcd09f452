"use client";
import Button from "@/lib/ui/components/global/Buttons/Button";
import {
    DashContent,
    DashContentAction,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Timer, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateDepartmentDialog from "./CreateDepartmentDialog";
import { getDepartments, deleteDepartment } from "@/lib/server/actions/department/DepartmentActions";
import { useRouter } from "next/navigation";
import { useTransition, useEffect, useState } from "react";

type Department = {
    id: number;
    name: string;
};

export default function DepartmentsPage() {
    const [isPending, startTransition] = useTransition();
    const router = useRouter();
    const [departments, setDepartments] = useState<Department[]>([]);

    useEffect(() => {
        getDepartments().then(data => setDepartments(data.departments || []));
    }, []);

    const handleDelete = async (id: number) => {
        await deleteDepartment(id);
        setDepartments(departments.filter(dep => dep.id !== id));
        router.refresh();
    };

    return (
        <DashContent>
            <DashContenTitle>Departments</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem title="Total Departments" value={departments.length.toString()} icon={<UserPen size={80} />} />
            </DashContentStat>
            <DashContentAction>
                <CreateDepartmentDialog />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Department Name', 'Settings']} />
                <tbody>
                    {departments.map((department) => (
                        <TableTr key={department.id}>
                            <TableTdMain value={department.name} />
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/departments/${department.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => startTransition(() => handleDelete(department.id))}
                                        disabled={isPending}
                                        style={{ background: 'none', border: 'none', padding: 0, margin: 0, cursor: 'pointer' }}
                                        title="Delete Department"
                                    >
                                        <Trash className="text-error dark:text-dark-error" size={16} />
                                    </button>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </DashContent>
    );
}
