<?php

namespace App\Http\Controllers\Api\Core;

use App\Http\Controllers\Controller;
use App\Models\Api\Core\Department;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DepartmentsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $departments = Department::with(['years.sections.groups'])
            ->paginate(10);
        
        return response()->json($departments);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:departments,name',
        ]);

        $department = Department::create([
            'name' => $request->name,
        ]);

        return response()->json($department->load(['years.sections.groups']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Department $department): JsonResponse
    {
        return response()->json($department->load(['years.sections.groups']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Department $department): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:departments,name,' . $department->id,
        ]);

        $department->update([
            'name' => $request->name,
        ]);

        return response()->json($department->load(['years.sections.groups']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Department $department): JsonResponse
    {
        // Delete all related years, sections, groups, students, and timetables
        foreach ($department->years as $year) {
            foreach ($year->sections as $section) {
                foreach ($section->groups as $group) {
                    $group->students()->delete();
                    if ($group->timeTable) {
                        $group->timeTable->days()->each(function ($day) {
                            $day->lessens()->delete();
                        });
                        $group->timeTable->days()->delete();
                        $group->timeTable->delete();
                    }
                    $group->delete();
                }
                if ($section->timeTable) {
                    $section->timeTable->days()->each(function ($day) {
                        $day->lessens()->delete();
                    });
                    $section->timeTable->days()->delete();
                    $section->timeTable->delete();
                }
                $section->delete();
            }
            $year->delete();
        }
        $department->delete();

        return response()->json(['message' => 'Department deleted successfully']);
    }
}
