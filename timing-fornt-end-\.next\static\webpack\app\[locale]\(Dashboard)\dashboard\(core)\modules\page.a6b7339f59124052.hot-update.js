"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/modules/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"666f258f2a54\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcTWVtb2lyZVxcY29kZSBsYXN0IHZlcnNpb24gVjNcXEZpIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NjZmMjU4ZjJhNTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/ui/forms/module/CreateModuleForm.tsx":
/*!******************************************************!*\
  !*** ./src/lib/ui/forms/module/CreateModuleForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateModuleForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_ui_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ui/components/global/Buttons/Button */ \"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx\");\n/* harmony import */ var _lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/server/actions/module/moduleActions */ \"(app-pages-browser)/./src/lib/server/actions/module/moduleActions.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst createModuleSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(1, \"Module name is required\")\n});\nfunction CreateModuleForm(param) {\n    let { onSuccess } = param;\n    _s();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, formState: { errors, isSubmitting }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(createModuleSchema)\n    });\n    const onSubmit = async (data)=>{\n        setError(null);\n        setSuccess(false);\n        try {\n            console.log('Attempting to create module with data:', data);\n            const response = await (0,_lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_4__.createModule)({\n                name: data.name\n            });\n            console.log('Create module response:', response);\n            // Check if response contains an error message\n            if (response && typeof response === 'object' && 'message' in response) {\n                console.log('Error response received:', response);\n                setError(response.message);\n                return;\n            }\n            // Check if response is a valid module object\n            if (response && typeof response === 'object' && 'id' in response) {\n                console.log('Module created successfully:', response);\n                setSuccess(true);\n                reset();\n                // Show success message briefly, then trigger refresh\n                setTimeout(()=>{\n                    onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                }, 800);\n            } else {\n                console.log('Unexpected response format:', response);\n                setError(\"Unexpected response from server\");\n            }\n        } catch (e) {\n            var _e_response, _e_response_data, _e_response1;\n            console.error('Create module error:', e);\n            console.error('Error details:', (_e_response = e.response) === null || _e_response === void 0 ? void 0 : _e_response.data);\n            setError(((_e_response1 = e.response) === null || _e_response1 === void 0 ? void 0 : (_e_response_data = _e_response1.data) === null || _e_response_data === void 0 ? void 0 : _e_response_data.message) || e.message || \"Failed to create module\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"flex flex-col gap-4 w-full max-w-md\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Module created successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                children: [\n                    \"Module Name\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        ...register(\"name\"),\n                        className: \"input\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500\",\n                        children: errors.name.message\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                type: \"submit\",\n                mode: \"filled\",\n                disabled: isSubmitting,\n                children: isSubmitting ? \"Creating...\" : \"Create Module\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memoire\\\\code last version V3\\\\Fi project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\module\\\\CreateModuleForm.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateModuleForm, \"5dqZERgP5KSXdgOdqVgEkrBbDjQ=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = CreateModuleForm;\nvar _c;\n$RefreshReg$(_c, \"CreateModuleForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ui/forms/module/CreateModuleForm.tsx\n"));

/***/ })

});