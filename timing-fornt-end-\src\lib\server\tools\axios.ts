import axios from 'axios';
import { cookies } from 'next/headers';
import { decrypt } from './session';

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BACKEND_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept':'application/json'
  },
});

// Add request interceptor to include auth token
axiosInstance.interceptors.request.use(
  async (config) => {
    try {
      console.log('Axios interceptor: Processing request to', config.url);
      const cookie = (await cookies()).get("session")?.value;
      console.log('Axios interceptor: <PERSON><PERSON> found:', !!cookie);

      if (cookie) {
        const session = await decrypt(cookie);
        console.log('Axios interceptor: Session decrypted:', !!session);
        const token = session?.token;
        console.log('Axios interceptor: Token found:', !!token);

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log('Axios interceptor: Authorization header set');
        }
      }
      return config;
    } catch (error) {
      console.error('Axios interceptor error:', error);
      return config; // Continue with request even if auth fails
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle errors here (e.g., 401 unauthorized, 403 forbidden)
    return Promise.reject(error);
  }
);

export default axiosInstance; 