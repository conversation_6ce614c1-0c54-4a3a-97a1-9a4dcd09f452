<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Api\Main\Lessen;
use App\Models\Api\Main\Day;
use App\Models\Api\Users\Teacher;
use App\Models\Api\Users\Admin;
use App\Models\Api\Users\Student;
use App\Models\Api\Main\Group;
use App\Models\Api\Core\Module;
use App\Models\Api\Core\ClassRome;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

echo "=== COMPLETE ADMIN LESSON WORKFLOW TEST ===\n\n";

// Get test data - specifically Teacher Lamia and Group 6
$teacher = Teacher::where('name', 'Lamia')->first();
if (!$teacher) {
    echo "❌ Teacher Lamia not found, using first teacher\n";
    $teacher = Teacher::first();
}

$admin = Admin::first();
$group = Group::where('number', 6)->first();
if (!$group) {
    echo "❌ Group 6 not found, using first group\n";
    $group = Group::first();
}

$student = Student::where('group_id', $group->id)->first();
$module = Module::first();
$classRome = ClassRome::first();

echo "=== TEST SETUP ===\n";
echo "👩‍🏫 Teacher: {$teacher->name} {$teacher->last} (ID: {$teacher->id})\n";
echo "👨‍💼 Admin: {$admin->name} {$admin->last} (ID: {$admin->id})\n";
echo "👥 Group: {$group->number} (ID: {$group->id})\n";
echo "👨‍🎓 Student: " . ($student ? "{$student->name} {$student->last} (ID: {$student->id})" : "None found") . "\n";
echo "📚 Module: {$module->name} (ID: {$module->id})\n";
echo "🏫 ClassRoom: {$classRome->number} (ID: {$classRome->id})\n\n";

// Step 1: Check initial state
echo "=== STEP 1: INITIAL STATE ===\n";
$initialTeacherLessons = Lessen::where('teacher_id', $teacher->id)->count();
echo "📊 Teacher {$teacher->name} currently has {$initialTeacherLessons} lessons\n\n";

// Step 2: Admin creates lesson
echo "=== STEP 2: ADMIN CREATES LESSON ===\n";
Auth::login($admin->key->user);
echo "✅ Authenticated as admin: {$admin->name}\n";

// Get a day from the group's timetable
$timeTable = $group->timeTable;
$day = $timeTable->days->where('name', 'tue')->first(); // Use Tuesday for testing

if (!$day) {
    echo "❌ Tuesday not found in group timetable\n";
    $day = $timeTable->days->first();
}

echo "📅 Using day: {$day->name} (ID: {$day->id}) from timetable ID: {$day->time_table_id}\n";

// Create lesson via admin (simulating admin dashboard action)
$controller = new \App\Http\Controllers\Api\Main\GroupsController();
$request = new Request();
$request->merge([
    'day_id' => $day->id,
    'start_time' => '10:00:00',
    'end_time' => '11:30:00',
    'type' => 'course',
    'module_id' => $module->id,
    'teacher_id' => $teacher->id,
    'class_rome_id' => $classRome->id,
]);

echo "\n🎯 Creating lesson with details:\n";
echo "   📅 Day: {$day->name}\n";
echo "   ⏰ Time: 10:00:00 - 11:30:00\n";
echo "   👩‍🏫 Teacher: {$teacher->name} {$teacher->last}\n";
echo "   📚 Module: {$module->name}\n";
echo "   🏫 Classroom: {$classRome->number}\n";

try {
    $response = $controller->reserveClassRome($request, $group);
    $data = $response->getData(true);
    
    echo "\n✅ Admin lesson creation successful!\n";
    echo "   📋 Status: " . $response->getStatusCode() . "\n";
    echo "   🆔 Lesson ID: " . ($data['id'] ?? 'None') . "\n";
    echo "   👩‍🏫 Teacher ID: " . ($data['teacher_id'] ?? 'None') . "\n";
    echo "   📅 Day ID: " . ($data['day_id'] ?? 'None') . "\n";
    
    $lessonId = $data['id'] ?? null;
    
} catch (Exception $e) {
    echo "\n❌ Error creating lesson: " . $e->getMessage() . "\n";
    exit;
}

// Step 3: Verify lesson in database
echo "\n=== STEP 3: DATABASE VERIFICATION ===\n";
if ($lessonId) {
    $lesson = Lessen::find($lessonId);
    if ($lesson) {
        echo "✅ Lesson found in database:\n";
        echo "   🆔 ID: {$lesson->id}\n";
        echo "   👩‍🏫 Teacher ID: {$lesson->teacher_id}\n";
        echo "   📅 Day ID: {$lesson->day_id}\n";
        echo "   ⏰ Time: {$lesson->start_time} - {$lesson->end_time}\n";
        echo "   📝 Type: {$lesson->type}\n";
        echo "   📚 Module: {$lesson->module->name}\n";
        echo "   👩‍🏫 Teacher: {$lesson->teacher->name} {$lesson->teacher->last}\n";
        echo "   🏫 Classroom: {$lesson->classRome->number}\n";
        echo "   📅 Day: {$lesson->day->name}\n";
    } else {
        echo "❌ Lesson not found in database!\n";
        exit;
    }
}

// Step 4: Check teacher lesson count
echo "\n=== STEP 4: TEACHER LESSON COUNT ===\n";
$newTeacherLessons = Lessen::where('teacher_id', $teacher->id)->count();
echo "📊 Teacher {$teacher->name} now has {$newTeacherLessons} lessons\n";
echo "📈 Increment: " . ($newTeacherLessons - $initialTeacherLessons) . "\n";

if ($newTeacherLessons > $initialTeacherLessons) {
    echo "✅ Teacher lesson count increased correctly\n";
} else {
    echo "❌ Teacher lesson count did not increase\n";
}

// Step 5: Test teacher timing API
echo "\n=== STEP 5: TEACHER TIMING API TEST ===\n";
Auth::login($teacher->key->user);
echo "✅ Authenticated as teacher: {$teacher->name}\n";

$timingController = new \App\Http\Controllers\Api\Main\GroupsController();
$timingResponse = $timingController->teacher();
$timingData = $timingResponse->getData(true);

echo "📡 Teacher timing API response:\n";
echo "   📋 Status: " . $timingResponse->getStatusCode() . "\n";

if (isset($timingData['lessons'])) {
    $totalApiLessons = 0;
    $foundNewLesson = false;
    
    foreach ($timingData['lessons'] as $dayName => $dayData) {
        if ($dayData && isset($dayData['lessens'])) {
            $dayLessons = count($dayData['lessens']);
            $totalApiLessons += $dayLessons;
            
            if ($dayLessons > 0) {
                echo "   📅 {$dayName}: {$dayLessons} lessons\n";
                
                // Check if our new lesson is in this day
                if ($dayName === $day->name) {
                    echo "      🔍 Checking {$dayName} for lesson ID {$lessonId}:\n";
                    foreach ($dayData['lessens'] as $apiLesson) {
                        echo "         - Lesson ID {$apiLesson['id']} at {$apiLesson['start_time']}-{$apiLesson['end_time']}\n";
                        if ($apiLesson['id'] == $lessonId) {
                            $foundNewLesson = true;
                            echo "      🎯 FOUND NEW LESSON: ID {$apiLesson['id']} at {$apiLesson['start_time']}-{$apiLesson['end_time']}\n";
                        }
                    }
                    if (!$foundNewLesson) {
                        echo "      ❌ New lesson ID {$lessonId} not found in {$dayName}\n";
                    }
                }
            }
        }
    }
    
    echo "   📊 Total lessons in API: {$totalApiLessons}\n";
    echo "   💾 Database count: {$newTeacherLessons}\n";
    
    if ($totalApiLessons === $newTeacherLessons) {
        echo "✅ API data matches database count\n";
    } else {
        echo "❌ API data mismatch - API: {$totalApiLessons}, DB: {$newTeacherLessons}\n";
    }
    
    if ($foundNewLesson) {
        echo "✅ NEW LESSON FOUND IN TEACHER TIMING API!\n";
    } else {
        echo "❌ New lesson NOT found in teacher timing API\n";
    }
} else {
    echo "❌ No lessons data in API response\n";
}

// Step 6: Test student timing (if student exists)
if ($student && $student->key && $student->key->user) {
    echo "\n=== STEP 6: STUDENT TIMING VERIFICATION ===\n";
    Auth::login($student->key->user);
    echo "✅ Authenticated as student: {$student->name}\n";
    
    $studentController = new \App\Http\Controllers\Api\Main\GroupsController();
    $studentResponse = $studentController->students();
    $studentData = $studentResponse->getData(true);
    
    echo "📡 Student timing API response:\n";
    echo "   📋 Status: " . $studentResponse->getStatusCode() . "\n";
    
    if (isset($studentData['timeTableGroup']['days'])) {
        $foundInStudentView = false;
        foreach ($studentData['timeTableGroup']['days'] as $studentDay) {
            if ($studentDay['name'] === $day->name && isset($studentDay['lessens'])) {
                foreach ($studentDay['lessens'] as $studentLesson) {
                    if ($studentLesson['id'] == $lessonId) {
                        $foundInStudentView = true;
                        echo "   🎯 FOUND NEW LESSON in student view: ID {$studentLesson['id']}\n";
                        break 2;
                    }
                }
            }
        }
        
        if ($foundInStudentView) {
            echo "✅ NEW LESSON FOUND IN STUDENT TIMING!\n";
        } else {
            echo "❌ New lesson NOT found in student timing\n";
        }
    }
}

// Step 7: Cleanup
echo "\n=== STEP 7: CLEANUP ===\n";
if ($lessonId) {
    $lesson = Lessen::find($lessonId);
    if ($lesson) {
        $lesson->delete();
        echo "🧹 Test lesson deleted (ID: {$lessonId})\n";
    }
}

// Final summary
echo "\n=== FINAL SUMMARY ===\n";
echo "🎯 Test Results:\n";
echo "   📝 Admin lesson creation: " . ($lessonId ? '✅' : '❌') . "\n";
echo "   💾 Database storage: " . ($lesson ? '✅' : '❌') . "\n";
echo "   📊 Teacher count increment: " . (($newTeacherLessons - $initialTeacherLessons) === 1 ? '✅' : '❌') . "\n";
echo "   📡 Teacher API consistency: " . (isset($totalApiLessons) && $totalApiLessons === $newTeacherLessons ? '✅' : '❌') . "\n";
echo "   🎯 Teacher timing display: " . (isset($foundNewLesson) && $foundNewLesson ? '✅' : '❌') . "\n";
echo "   👨‍🎓 Student timing display: " . (isset($foundInStudentView) && $foundInStudentView ? '✅' : '❌') . "\n";

if (isset($foundNewLesson) && $foundNewLesson) {
    echo "\n🎉 SUCCESS! Admin-created lessons now appear in teacher timing tables!\n";
    echo "The cache invalidation fix is working correctly.\n";
} else {
    echo "\n⚠️  Issue detected: Admin-created lesson not appearing in teacher timing.\n";
    echo "This may indicate a cache invalidation or API issue.\n";
}

echo "\n=== TEST COMPLETE ===\n";
