"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Trash, <PERSON>ert<PERSON><PERSON>gle, Loader2 } from "lucide-react";
import { deleteModule } from "@/lib/server/actions/module/moduleActions";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import Button from "@/lib/ui/components/global/Buttons/Button";

interface DeleteModuleProps {
    moduleId: number;
    moduleName: string;
}

export default function DeleteModule({ moduleId, moduleName }: DeleteModuleProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();

    const handleDelete = async () => {
        setIsDeleting(true);
        setError(null);
        
        try {
            const result = await deleteModule(moduleId);
            
            // Check if the result contains an error message
            if (result && typeof result === 'object' && 'message' in result) {
                setError(result.message);
                setIsDeleting(false);
                return;
            }
            
            // Success - close dialog and refresh
            setIsOpen(false);
            router.refresh();
        } catch (err: any) {
            console.error('Delete module error:', err);
            setError(err.message || 'Failed to delete module');
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <>
            <button
                onClick={() => setIsOpen(true)}
                className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                title={`Delete ${moduleName}`}
            >
                <Trash className="text-error dark:text-dark-error" size={16} />
            </button>

            <Dialog 
                isOpen={isOpen} 
                onClose={() => !isDeleting && setIsOpen(false)} 
                title="Delete Module"
            >
                <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                        <AlertTriangle className="text-red-500 mt-1 flex-shrink-0" size={20} />
                        <div>
                            <p className="text-gray-900 dark:text-gray-100 font-medium">
                                Are you sure you want to delete this module?
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 mt-1">
                                <strong>"{moduleName}"</strong> will be permanently deleted. This action cannot be undone.
                            </p>
                            <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                                ⚠️ This will also affect any sessions/lessons associated with this module.
                            </p>
                        </div>
                    </div>

                    {error && (
                        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
                        </div>
                    )}

                    <div className="flex gap-3 justify-end">
                        <Button
                            mode="outline"
                            onClick={() => setIsOpen(false)}
                            disabled={isDeleting}
                        >
                            Cancel
                        </Button>
                        <Button
                            mode="filled"
                            onClick={handleDelete}
                            disabled={isDeleting}
                            className="bg-red-600 hover:bg-red-700 text-white"
                        >
                            {isDeleting ? (
                                <>
                                    <Loader2 className="animate-spin" size={16} />
                                    Deleting...
                                </>
                            ) : (
                                <>
                                    <Trash size={16} />
                                    Delete Module
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </Dialog>
        </>
    );
}
