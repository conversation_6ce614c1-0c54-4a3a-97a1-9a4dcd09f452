"use server"

import { TimeTableResponse } from "@/lib/server/types/sectionTiming/sectionTiming";
import axiosInstance from "../../tools/axios";
import { ClassRoomPayload, ClassroomsResponse } from "../../types/validClasses/ValidClasses";
import { DaysResponse } from "../../types/days/days";
import { ModulesAllResponse } from "../../types/modulesAll/ModulesAll";
import { TeachersResponse } from "../../types/teachersAll/teachersAll";
import { revalidatePath } from "next/cache";

export async function getSectionTiming(sectionId: number): Promise<TimeTableResponse> {
    try {
        const { data } = await axiosInstance.get(`sections/${sectionId}/time-table`);
        return data;
    } catch (error) {
        console.error("Error fetching section timing:", error);
        throw error;
    }
}

export async function validClassRoom(sectionId: number, data_payload: ClassRoomPayload): Promise<ClassroomsResponse> {
    try {
        const { data } = await axiosInstance.post(`sections/${sectionId}/valid-classes`, data_payload);
        return data;
    } catch (error) {
        console.error("Error fetching section timing:", error.response.data);
        throw error;
    }

}

export async function reserveClassRome(sectionId: number, data_payload: ClassRoomPayload): Promise<{ message: string, success: boolean }> {
    try {
        console.log('Creating lesson via admin for section:', sectionId, data_payload);
        const { data } = await axiosInstance.post(`sections/${sectionId}/reserve-class-rome`, data_payload);
        console.log('Admin lesson creation response:', data);

        // Comprehensive cache invalidation for all affected areas
        console.log('Invalidating cache after admin lesson creation...');

        // Teacher timing paths (most important for this fix)
        revalidatePath('/', 'layout') // Teacher guest timing page
        revalidatePath('/teacher')

        // Section timing paths
        revalidatePath('/dashboard/sections')
        revalidatePath('/dashboard/sections/timing')
        revalidatePath(`/dashboard/sections/timing/${sectionId}`)

        // Group timing paths (lessons in sections affect groups)
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups/timing')

        // Student timing paths
        revalidatePath('/student')

        // General dashboard
        revalidatePath('/dashboard')

        console.log('Cache invalidated successfully after admin lesson creation');

        return {
            message: "Class reserved successfully",
            success: true
        };
    } catch (error) {
        console.error("Error creating lesson via admin:", error.response?.data || error);
        return {
            message: "Class not reserved",
            success: false
        };
    }
}

export async function deleteSession(sessionId: number): Promise<{ message: string, success: boolean }> {
    try {
        console.log('Deleting lesson:', sessionId);
        await axiosInstance.delete(`lessens/${sessionId}`);

        // Invalidate cache after lesson deletion
        console.log('Invalidating cache after lesson deletion...');
        revalidatePath('/', 'layout') // Teacher guest timing page
        revalidatePath('/teacher')
        revalidatePath('/dashboard/sections')
        revalidatePath('/dashboard/sections/timing')
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups/timing')
        revalidatePath('/student')
        revalidatePath('/dashboard')
        console.log('Cache invalidated after lesson deletion');

        return {
            message: "Session deleted successfully",
            success: true
        };
    } catch (error) {
        console.error("Error deleting session:", error.response?.data || error);
        return {
            message: "Session not deleted",
            success: false
        };
    }
}

export async function getDays(sectionId: number): Promise<DaysResponse> {
    try {
        const { data } = await axiosInstance.get(`days/${sectionId}`);
        return data;
    } catch (error) {
        console.error("Error creating lessen timing:", error.response.data);
        throw error;
    }
}

export async function getModules(): Promise<ModulesAllResponse> {
    try {
        const { data } = await axiosInstance.get(`modulesAll`);
        return data;
    } catch (error) {
        console.error("Error creating lessen timing:", error.response.data);
        throw error;
    }
}

export async function getTeachers(): Promise<TeachersResponse> {
    try {
        const { data } = await axiosInstance.get(`teachersAll`);
        return data;
    } catch (error) {
        console.error("Error creating lessen timing:", error.response.data);
        throw error;
    }
}
