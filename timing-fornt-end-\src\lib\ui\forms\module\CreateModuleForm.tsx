"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { createModule } from "@/lib/server/actions/module/moduleActions";
import { CheckCircle2, AlertCircle } from "lucide-react";

const createModuleSchema = z.object({
  name: z.string().min(1, "Module name is required"),
});

type CreateModuleFormData = z.infer<typeof createModuleSchema>;

export default function CreateModuleForm({ onSuccess }: { onSuccess?: () => void }) {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateModuleFormData>({
    resolver: zodResolver(createModuleSchema),
  });

  const onSubmit = async (data: CreateModuleFormData) => {
    setError(null);
    setSuccess(false);
    try {
      console.log('Attempting to create module with data:', data);
      const response = await createModule({ name: data.name });
      console.log('Create module response:', response);

      // Check if response contains an error message
      if (response && typeof response === 'object' && 'message' in response) {
        console.log('Error response received:', response);
        setError((response as any).message);
        return;
      }

      // Check if response is a valid module object
      if (response && typeof response === 'object' && 'id' in response) {
        console.log('Module created successfully:', response);
        setSuccess(true);
        reset();

        // Show success message briefly, then trigger refresh
        setTimeout(() => {
          onSuccess?.();
        }, 800);
      } else {
        console.log('Unexpected response format:', response);
        setError("Unexpected response from server");
      }
    } catch (e: any) {
      console.error('Create module error:', e);
      console.error('Error details:', e.response?.data);
      setError(e.response?.data?.message || e.message || "Failed to create module");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Module created successfully!</span>
        </div>
      )}
      <label>
        Module Name
        <input type="text" {...register("name")} className="input" />
        {errors.name && <span className="text-red-500">{errors.name.message}</span>}
      </label>
      <Button type="submit" mode="filled" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Module"}
      </Button>
    </form>
  );
}
