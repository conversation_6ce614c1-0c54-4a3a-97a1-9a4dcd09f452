"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-intl";
exports.ids = ["vendor-chunks/use-intl"];
exports.modules = {

/***/ "(rsc)/./node_modules/use-intl/dist/esm/development/core.js":
/*!************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/core.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   _createCache: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   createFormatter: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createTranslator: () => (/* binding */ createTranslator),\n/* harmony export */   hasLocale: () => (/* binding */ hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.i)\n/* harmony export */ });\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n\n\n\n\n\n\nfunction createTranslatorImpl({\n  messages,\n  namespace,\n  ...rest\n}, namespacePrefix) {\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.r)(namespace, namespacePrefix);\n  return (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.e)({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n// This type is slightly more loose than `AbstractIntlMessages`\n// in order to avoid a type error.\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator({\n  _cache = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.d)(),\n  _formatters = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.b)(_cache),\n  getMessageFallback = _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.f,\n  messages,\n  namespace,\n  onError = _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.g,\n  ...rest\n}) {\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? `!.${namespace}` : '!'\n  }, '!');\n}\n\n/**\n * Checks if a locale exists in a list of locales.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale\n */\nfunction hasLocale(locales, candidate) {\n  return locales.includes(candidate);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/esm/development/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I: () => (/* binding */ IntlError),\n/* harmony export */   a: () => (/* binding */ IntlErrorCode),\n/* harmony export */   b: () => (/* binding */ createIntlFormatters),\n/* harmony export */   c: () => (/* binding */ createFormatter),\n/* harmony export */   d: () => (/* binding */ createCache),\n/* harmony export */   e: () => (/* binding */ createBaseTranslator),\n/* harmony export */   f: () => (/* binding */ defaultGetMessageFallback),\n/* harmony export */   g: () => (/* binding */ defaultOnError),\n/* harmony export */   i: () => (/* binding */ initializeConfig),\n/* harmony export */   r: () => (/* binding */ resolveNamespace)\n/* harmony export */ });\n/* harmony import */ var intl_messageformat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\n\n\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.date;\n  const mfTimeDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.memoize)(fn, {\n    cache: createMemoCache(cache),\n    strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(result) ? /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I: () => (/* binding */ IntlError),\n/* harmony export */   a: () => (/* binding */ IntlErrorCode),\n/* harmony export */   b: () => (/* binding */ createIntlFormatters),\n/* harmony export */   c: () => (/* binding */ createFormatter),\n/* harmony export */   d: () => (/* binding */ createCache),\n/* harmony export */   e: () => (/* binding */ createBaseTranslator),\n/* harmony export */   f: () => (/* binding */ defaultGetMessageFallback),\n/* harmony export */   g: () => (/* binding */ defaultOnError),\n/* harmony export */   i: () => (/* binding */ initializeConfig),\n/* harmony export */   r: () => (/* binding */ resolveNamespace)\n/* harmony export */ });\n/* harmony import */ var intl_messageformat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\n\n\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.date;\n  const mfTimeDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.memoize)(fn, {\n    cache: createMemoCache(cache),\n    strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(result) ? /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/esm/development/react.js":
/*!*************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/react.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlProvider: () => (/* binding */ IntlProvider),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* binding */ useLocale),\n/* harmony export */   useMessages: () => (/* binding */ useMessages),\n/* harmony export */   useNow: () => (/* binding */ useNow),\n/* harmony export */   useTimeZone: () => (/* binding */ useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\nconst IntlContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n\nfunction IntlProvider({\n  children,\n  formats,\n  getMessageFallback,\n  locale,\n  messages,\n  now,\n  onError,\n  timeZone\n}) {\n  const prevContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return prevContext?.cache || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.d)();\n  }, [locale, prevContext?.cache]);\n  const formatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => prevContext?.formatters || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.b)(cache), [cache, prevContext?.formatters]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    ...(0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.i)({\n      locale,\n      // (required by provider)\n      formats: formats === undefined ? prevContext?.formats : formats,\n      getMessageFallback: getMessageFallback || prevContext?.getMessageFallback,\n      messages: messages === undefined ? prevContext?.messages : messages,\n      now: now || prevContext?.now,\n      onError: onError || prevContext?.onError,\n      timeZone: timeZone || prevContext?.timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, formats, formatters, getMessageFallback, locale, messages, now, onError, prevContext, timeZone]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(IntlContext.Provider, {\n    value: value,\n    children: children\n  });\n}\n\nfunction useIntlContext() {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n  if (!context) {\n    throw new Error('No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#server-client-components' );\n  }\n  return context;\n}\n\nlet hasWarnedForMissingTimezone = false;\nconst isServer = typeof window === 'undefined';\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n  const {\n    cache,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback,\n    locale,\n    onError,\n    timeZone\n  } = useIntlContext();\n\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the hook invocation.\n  const allMessages = allMessagesPrefixed[namespacePrefix];\n  const namespace = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.r)(namespacePrefixed, namespacePrefix);\n  if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasWarnedForMissingTimezone = true;\n    onError(new _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.I(_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.a.ENVIRONMENT_FALLBACK, `There is no \\`timeZone\\` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone` ));\n  }\n  const translate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.e)({\n    cache,\n    formatters,\n    getMessageFallback,\n    messages: allMessages,\n    namespace,\n    onError,\n    formats: globalFormats,\n    locale,\n    timeZone\n  }), [cache, formatters, getMessageFallback, allMessages, namespace, onError, globalFormats, locale, timeZone]);\n  return translate;\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction useTranslations(namespace) {\n  const context = useIntlContext();\n  const messages = context.messages;\n\n  // We have to wrap the actual hook so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return useTranslationsImpl({\n    '!': messages\n  },\n  // @ts-expect-error\n  namespace ? `!.${namespace}` : '!', '!');\n}\n\nfunction useLocale() {\n  return useIntlContext().locale;\n}\n\nfunction getNow() {\n  return new Date();\n}\n\n/**\n * @see https://next-intl.dev/docs/usage/dates-times#relative-times-usenow\n */\nfunction useNow(options) {\n  const updateInterval = options?.updateInterval;\n  const {\n    now: globalNow\n  } = useIntlContext();\n  const [now, setNow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalNow || getNow());\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!updateInterval) return;\n    const intervalId = setInterval(() => {\n      setNow(getNow());\n    }, updateInterval);\n    return () => {\n      clearInterval(intervalId);\n    };\n  }, [globalNow, updateInterval]);\n  return updateInterval == null && globalNow ? globalNow : now;\n}\n\nfunction useTimeZone() {\n  return useIntlContext().timeZone;\n}\n\nfunction useMessages() {\n  const context = useIntlContext();\n  if (!context.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages' );\n  }\n  return context.messages;\n}\n\nfunction useFormatter() {\n  const {\n    formats,\n    formatters,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone\n  } = useIntlContext();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.c)({\n    formats,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone,\n    _formatters: formatters\n  }), [formats, formatters, globalNow, locale, onError, timeZone]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/esm/development/react.js\n");

/***/ })

};
;