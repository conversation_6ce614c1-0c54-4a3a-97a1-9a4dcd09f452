"use client";

import { TeacherTimingResponse } from "@/lib/server/types/sectionTiming/teacherTiming";
import {
    DashContent,
    DashContentAction,
    DashContenTitle,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr,
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import TeacherValidClasses from "@/lib/ui/forms/TimingForms/TeacherValidClasses";
import { useState } from "react";
import Link from "next/link";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { Search } from "lucide-react";
import PrintButton from "../student/PrintButton";

const STANDARD_SLOTS = [
    { start: "08:00", end: "09:30" },
    { start: "09:30", end: "11:00" },
    { start: "11:00", end: "12:30" },
    { start: "12:30", end: "14:00" },
    { start: "14:00", end: "15:30" },
    { start: "15:30", end: "17:00" },
];
const DAY_LABELS = {
    sat: "Saturday",
    sun: "Sunday",
    mon: "Monday",
    tues: "Tuesday",
    wed: "Wednesday",
    thu: "Thursday",
};
const DAYS = ["sat", "sun", "mon", "tues", "wed", "thu"];

export default function Index({
    data,
    user,
}: {
    data: TeacherTimingResponse;
    user: any;
}) {
    const [timingDate] = useState<TeacherTimingResponse>(data);

    return (
        <DashContent>
            <DashContenTitle>
                Timing teacher : {user?.user.key.keyable.name} - {user?.user.key.keyable.last}
            </DashContenTitle>
            <DashContentAction>
                <TeacherValidClasses />
                <Link href="/teacher/groups-timetables">
                    <Button icon={<Search />} mode="outlined">
                        Search All Groups Timetables
                    </Button>
                </Link>
            </DashContentAction>
            <div className="flex flex-col gap-4">
                <div id="printable-timetable">
                    <DashContentTable>
                        <TableThead list={["Day", ...STANDARD_SLOTS.map(slot => `${slot.start}–${slot.end}`)]} />
                        <tbody>
                            {DAYS.map(dayKey => {
                                const dayLessons = timingDate.lessons[dayKey];
                                const lessons = dayLessons?.lessens || [];
                                return (
                                    <TableTr key={dayKey}>
                                        <TableTdMain value={DAY_LABELS[dayKey]} />
                                        {STANDARD_SLOTS.map(slot => {
                                            let session = null;
                                            if (lessons.length > 0) {
                                                session = lessons.find(
                                                    l => l.start_time.slice(0,5) === slot.start && l.end_time.slice(0,5) === slot.end
                                                );
                                            }
                                            return (
                                                <TableTd key={slot.start}>
                                                    {session ? (
                                                        <div className="flex flex-col gap-0.5 p-1">
                                                            <span className="font-semibold text-xs">
                                                                {session.module.name}
                                                                <span className="ml-1 px-1 py-0.5 rounded bg-blue-100 text-blue-700 text-[10px] font-bold uppercase align-middle">{session.type}</span>
                                                            </span>
                                                            <span className="text-[11px] text-gray-500">
                                                                {session.teacher.name} {session.teacher.last} &bull; class: {session.class_rome.number}
                                                            </span>
                                                        </div>
                                                    ) : (
                                                        <div>—</div>
                                                    )}
                                                </TableTd>
                                            );
                                        })}
                                    </TableTr>
                                );
                            })}
                        </tbody>
                    </DashContentTable>
                </div>
                <div className="flex justify-center mt-6">
                    <PrintButton />
                </div>
            </div>
        </DashContent>
    );
}
