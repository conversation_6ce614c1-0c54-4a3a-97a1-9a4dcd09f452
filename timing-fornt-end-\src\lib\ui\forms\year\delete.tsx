"use client";

import { Trash2 } from "lucide-react";
import { deleteYear } from "@/lib/server/actions/year/yearActions";
import { useRouter } from "next/navigation";
import { useState } from "react";

type YearWithDepartment = {
    id: number;
    name: string;
    year: number;
    department: {
        id: number;
        name: string;
    };
};

interface DeleteYearProps {
    year: YearWithDepartment;
}

export default function DeleteYear({ year }: DeleteYearProps) {
    const router = useRouter();
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = async () => {
        // Add confirmation dialog
        const confirmed = window.confirm(
            `Are you sure you want to delete "${year.name}"? This action cannot be undone.`
        );

        if (!confirmed) return;

        try {
            setIsDeleting(true);
            await deleteYear(year.id);
            router.refresh();
        } catch (error) {
            console.error('Error deleting year:', error);
            alert('Failed to delete year. Please try again.');
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <button
            onClick={handleDelete}
            disabled={isDeleting}
            className={`
                text-red-700 dark:text-red-400 
                hover:text-red-800 dark:hover:text-red-300 
                disabled:opacity-50
                transition-all duration-200
                ${isDeleting ? 'animate-spin' : ''}
            `}
        >
            <Trash2 size={16} />
        </button>
    );
}
